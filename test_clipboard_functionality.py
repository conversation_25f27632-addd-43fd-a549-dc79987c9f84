#!/usr/bin/env python3
"""
Test script to verify the enhanced clipboard monitoring functionality
"""

def is_direct_image_url(url):
    """Check if URL is a direct image URL based on file extension or common image hosting patterns."""
    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.svg']
    url_lower = url.lower()

    # First check for known webpage patterns that should NOT be treated as direct images
    webpage_patterns = ['freepik.com/free-', 'shutterstock.com/image-', 'stock.adobe.com/images/',
                       'dreamstime.com/stock-', 'pixabay.com/photos/', 'unsplash.com/photos/']

    if any(pattern in url_lower for pattern in webpage_patterns):
        return False

    # Check for direct file extensions
    if any(url_lower.endswith(ext) for ext in image_extensions):
        return True

    # Check for image hosting patterns (CDN/direct image hosts)
    image_hosts = ['img.freepik.com', 'image.shutterstock.com', 'as1.ftcdn.net', 'as2.ftcdn.net',
                   'thumbs.dreamstime.com', 'cdn.pixabay.com', 'images.unsplash.com']

    if any(host in url_lower for host in image_hosts):
        return True

    # Check for common image URL patterns (but only if not already excluded above)
    if any(pattern in url_lower for pattern in ['/images/', '/img/', '/photos/', '/pictures/']):
        return True

    return False

# Test URLs
test_urls = [
    # Webpage URLs (should be processed as webpage)
    "https://www.freepik.com/free-photo/home-indoor-design-concept_11904496.htm#fromView=author&page=1&position=0&uuid=662c73b5-06b5-4cec-818d-f0bb179a105e",
    "https://www.shutterstock.com/image-photo/beautiful-landscape-123456789",
    "https://stock.adobe.com/images/nature-scene/123456789",
    
    # Direct image URLs (should be processed as direct image)
    "https://img.freepik.com/free-photo/beautiful-landscape.jpg",
    "https://image.shutterstock.com/image-photo/nature-scene-450w-123456789.jpg",
    "https://as1.ftcdn.net/v2/jpg/01/23/45/67/1000_F_123456789_abcdef.jpg",
    "https://cdn.pixabay.com/photo/2023/01/01/12/00/nature-123456_1920.jpg",
    "https://images.unsplash.com/photo-1234567890?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80",
    "https://example.com/images/photo.png",
    "https://website.com/img/picture.webp",
    "https://site.com/photos/image.jpeg",
    
    # Regular URLs (should be processed as webpage)
    "https://www.google.com",
    "https://example.com/article/something"
]

print("Testing URL classification:")
print("=" * 50)

for url in test_urls:
    is_direct = is_direct_image_url(url)
    classification = "Direct Image URL" if is_direct else "Webpage URL"
    print(f"✓ {classification}: {url}")

print("\n" + "=" * 50)
print("Summary:")
print(f"Total URLs tested: {len(test_urls)}")
print(f"Direct image URLs: {sum(1 for url in test_urls if is_direct_image_url(url))}")
print(f"Webpage URLs: {sum(1 for url in test_urls if not is_direct_image_url(url))}")

print("\n🎉 URL classification test completed!")
print("\nClipboard monitoring will now:")
print("• Process direct image URLs immediately")
print("• Extract images from webpage URLs automatically")
print("• Provide helpful error messages when extraction fails")
print("• Support manual fallback with direct image URL input")
