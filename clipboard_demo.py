#!/usr/bin/env python3
"""
Demo script showing the enhanced clipboard monitoring functionality
"""

import time
import pyperclip

def is_direct_image_url(url):
    """Check if URL is a direct image URL based on file extension or common image hosting patterns."""
    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.svg']
    url_lower = url.lower()
    
    # First check for known webpage patterns that should NOT be treated as direct images
    webpage_patterns = ['freepik.com/free-', 'freepik.com/premium-', 'shutterstock.com/image-', 
                       'stock.adobe.com/images/', 'dreamstime.com/stock-', 'pixabay.com/photos/', 
                       'unsplash.com/photos/', 'pexels.com/photo/']
    
    if any(pattern in url_lower for pattern in webpage_patterns):
        return False
    
    # Check for direct file extensions
    if any(url_lower.endswith(ext) for ext in image_extensions):
        return True
    
    # Check for image hosting patterns (CDN/direct image hosts)
    # These are actual image file URLs, not webpage URLs
    image_hosts = ['img.freepik.com', 'as1.ftcdn.net', 'as2.ftcdn.net', 'thumbs.dreamstime.com', 
                   'cdn.pixabay.com', 'images.unsplash.com', 'images.pexels.com']
    
    # For these hosts, check if it's actually an image file
    for host in image_hosts:
        if host in url_lower:
            # Additional check: make sure it has image extension or image path patterns
            if any(ext in url_lower for ext in image_extensions) or any(pattern in url_lower for pattern in ['/jpg/', '/png/', '/webp/', '/jpeg/']):
                return True
        
    # Check for common image URL patterns (but only if not already excluded above)
    if any(pattern in url_lower for pattern in ['/images/', '/img/', '/photos/', '/pictures/']):
        return True
    
    return False

def simulate_clipboard_monitoring():
    """Simulate the enhanced clipboard monitoring"""
    print("🎉 Enhanced MetaMaster Clipboard Monitoring Demo")
    print("=" * 50)
    print("The clipboard monitor now supports:")
    print("✅ Direct image URLs (processed immediately)")
    print("✅ Webpage URLs (extracts image automatically)")
    print("✅ Better error handling and user feedback")
    print("✅ Manual fallback for blocked websites")
    print()
    
    # Test URLs to demonstrate
    test_urls = [
        "https://www.freepik.com/free-photo/home-indoor-design-concept_11904496.htm",
        "https://img.freepik.com/free-photo/beautiful-landscape.jpg",
        "https://as1.ftcdn.net/v2/jpg/01/23/45/67/1000_F_123456789_abcdef.jpg"
    ]
    
    print("Demo: Simulating clipboard content detection...")
    print()
    
    for i, url in enumerate(test_urls, 1):
        print(f"📋 Clipboard content {i}: {url}")
        
        if url.startswith("http"):
            if is_direct_image_url(url):
                print("   🖼️  Detected: Direct image URL")
                print("   ⚡ Action: Processing image immediately...")
                print("   ✅ Result: Prompt generated successfully!")
            else:
                print("   🌐 Detected: Webpage URL")
                print("   🔍 Action: Extracting image from webpage...")
                if "freepik.com" in url:
                    print("   ⚠️  Result: Access denied (403) - Website blocking detected")
                    print("   💡 Tip: Right-click image → Copy image address for direct processing")
                else:
                    print("   ✅ Result: Image extracted and prompt generated!")
        else:
            print("   ❌ Not a valid URL")
        
        print()
        time.sleep(1)  # Simulate processing time
    
    print("🎯 Key Features:")
    print("• Automatic detection of URL type (direct image vs webpage)")
    print("• Smart image extraction from popular stock photo sites")
    print("• Helpful error messages when websites block access")
    print("• Manual input option for direct image URLs")
    print("• Enhanced user feedback and logging")
    print()
    print("🚀 Ready to use! Start clipboard monitoring in MetaMaster!")

if __name__ == "__main__":
    simulate_clipboard_monitoring()
