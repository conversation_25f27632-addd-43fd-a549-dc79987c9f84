# MetaMaster Clipboard Monitoring Enhancement

## 🎉 New Features Added

The MetaMaster prompt generator now supports **automatic image extraction from webpage URLs** in addition to direct image URLs through clipboard monitoring!

### ✨ What's New

1. **Smart URL Detection**: Automatically detects whether a clipboard URL is:
   - 🖼️ **Direct Image URL** (processed immediately)
   - 🌐 **Webpage URL** (extracts image automatically)

2. **Enhanced Webpage Support**: Extracts images from popular stock photo websites:
   - Freepik.com
   - Shutterstock.com
   - Adobe Stock
   - Pixabay
   - Unsplash
   - And more!

3. **Improved Error Handling**: 
   - Better error messages when websites block access
   - Helpful tips for manual workarounds
   - Graceful fallback options

4. **Manual Fallback Option**: 
   - New "Direct Image URL" input field
   - Use when automatic extraction fails
   - Right-click → Copy image address workflow

## 🚀 How to Use

### Method 1: Clipboard Monitoring (Automatic)
1. Click "📋 Start Clipboard Monitor" in MetaMaster
2. Copy any image webpage URL (like the Freepik link you provided)
3. MetaMaster will automatically:
   - Detect if it's a webpage or direct image URL
   - Extract the image from the webpage
   - Generate prompts automatically

### Method 2: Manual Input
1. Paste webpage URL in "Webpage URL" field
2. Click "🔗 Process Webpage"
3. If extraction fails, right-click the image and copy image address
4. Paste direct image URL in "Direct Image URL" field
5. Click "🖼️ Process Image URL"

## 📋 Example URLs Supported

### Webpage URLs (Auto-extracted):
```
https://www.freepik.com/free-photo/home-indoor-design-concept_11904496.htm
https://www.shutterstock.com/image-photo/beautiful-landscape-123456
https://stock.adobe.com/images/nature-scene/123456
```

### Direct Image URLs (Immediate processing):
```
https://img.freepik.com/free-photo/beautiful-landscape.jpg
https://as1.ftcdn.net/v2/jpg/01/23/45/67/1000_F_123456789_abcdef.jpg
https://cdn.pixabay.com/photo/2023/01/01/12/00/nature-123456_1920.jpg
```

## 🛠️ Technical Improvements

1. **Enhanced Headers**: Better browser simulation to avoid bot detection
2. **Multiple Selectors**: Tries various CSS selectors for different websites
3. **Fallback Methods**: JavaScript parsing, og:image tags, generic image detection
4. **Session Management**: Uses requests.Session for better compatibility
5. **Smart Classification**: Distinguishes between webpage and direct image URLs

## ⚠️ Known Limitations

- Some websites (like Freepik) may block automated requests with 403 errors
- When this happens, use the manual fallback method:
  1. Right-click on the image
  2. Select "Copy image address"
  3. Paste in the "Direct Image URL" field

## 🎯 Benefits

- **Faster Workflow**: No need to manually extract image URLs
- **Better User Experience**: Clear feedback and error messages
- **Flexible Options**: Multiple ways to process images
- **Robust Handling**: Works even when websites change their structure

## 🔧 Files Modified

- `000/Prompts Master.py`: Enhanced clipboard monitoring and image extraction
- Added new functions:
  - `is_direct_image_url()`: Smart URL classification
  - `process_webpage_url_from_clipboard()`: Automatic webpage processing
  - `process_direct_image_url()`: Manual direct image processing
  - Enhanced `extract_image_from_webpage()`: Better extraction logic

## 🚀 Ready to Use!

The enhanced clipboard monitoring is now active and ready to process both direct image URLs and webpage URLs automatically. Simply start the clipboard monitor and copy any image-related URL to see it in action!
