#!/usr/bin/env python3
"""
Test script to verify Freepik image extraction functionality
"""

import requests
from bs4 import BeautifulSoup
import re

def extract_image_from_webpage(webpage_url):
    """Extract the main image from a webpage URL (like Freepik, Shutterstock, etc.)"""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(webpage_url, headers=headers, timeout=10)
        response.raise_for_status()

        soup = BeautifulSoup(response.text, "html.parser")

        # Try different selectors for different platforms
        image_url = None

        # Freepik specific selectors
        if "freepik.com" in webpage_url:
            # Try multiple Freepik selectors (they change frequently)
            img_tag = soup.find("img", {"class": "showcase__image"}) or \
                     soup.find("img", {"data-testid": "showcase-image"}) or \
                     soup.find("img", {"class": "image"}) or \
                     soup.find("img", {"class": "resource-image"}) or \
                     soup.find("img", {"class": "preview-image"}) or \
                     soup.find("img", {"data-testid": "resource-image"}) or \
                     soup.find("div", {"class": "showcase"}).find("img") if soup.find("div", {"class": "showcase"}) else None or \
                     soup.find("meta", {"property": "og:image"})

            if img_tag:
                if img_tag.name == "meta":
                    image_url = img_tag.get("content")
                else:
                    image_url = img_tag.get("src") or img_tag.get("data-src") or img_tag.get("data-lazy-src")
                    
            # Additional Freepik fallback - look for high-res image in script tags
            if not image_url:
                scripts = soup.find_all("script")
                for script in scripts:
                    if script.string and "preview" in script.string and ".jpg" in script.string:
                        # Look for image URLs in JavaScript
                        matches = re.findall(r'https://[^"\']*\.jpg[^"\']*', script.string)
                        if matches:
                            # Get the largest/highest quality image
                            for match in matches:
                                if "626" in match or "premium" in match or "preview" in match:
                                    image_url = match
                                    break
                            if not image_url and matches:
                                image_url = matches[0]

        # Generic fallback - try og:image meta tag
        if not image_url:
            og_image = soup.find("meta", {"property": "og:image"})
            if og_image:
                image_url = og_image.get("content")

        # Generic fallback - find largest image
        if not image_url:
            img_tags = soup.find_all("img")
            for img in img_tags:
                src = img.get("src") or img.get("data-src")
                if src and any(ext in src.lower() for ext in ['.jpg', '.jpeg', '.png', '.webp']):
                    # Skip small images (thumbnails, icons, etc.)
                    if not any(skip in src.lower() for skip in ['thumb', 'icon', 'logo', 'avatar', 'small']):
                        image_url = src
                        break

        if image_url:
            # Make sure URL is absolute
            if image_url.startswith("//"):
                image_url = "https:" + image_url
            elif image_url.startswith("/"):
                from urllib.parse import urljoin
                image_url = urljoin(webpage_url, image_url)

            return image_url

        return None

    except Exception as e:
        print(f"Error extracting image from webpage: {e}")
        return None

def is_direct_image_url(url):
    """Check if URL is a direct image URL based on file extension."""
    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.svg']
    url_lower = url.lower()
    return any(url_lower.endswith(ext) for ext in image_extensions) or any(ext in url_lower for ext in image_extensions)

# Test with the provided Freepik URL
test_url = "https://www.freepik.com/free-photo/home-indoor-design-concept_11904496.htm#fromView=author&page=1&position=0&uuid=662c73b5-06b5-4cec-818d-f0bb179a105e"

print(f"Testing URL: {test_url}")
print(f"Is direct image URL: {is_direct_image_url(test_url)}")
print("Extracting image...")

extracted_image = extract_image_from_webpage(test_url)

if extracted_image:
    print(f"✅ Successfully extracted image: {extracted_image}")
else:
    print("❌ Failed to extract image")

print("\nDone!")
